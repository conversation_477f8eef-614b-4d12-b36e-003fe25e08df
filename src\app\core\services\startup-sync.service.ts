import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, forkJoin, of } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { SupabaseApiService } from './supabase-api.service';
import { ContentSyncService } from './content-sync.service';
import { DeviceRegistrationService } from './device-registration.service';
import { ScheduleService } from './schedule.service';
import { LogService } from './log.service';
import { Playlist } from '../models/playlist.model';
import { Screen } from '../models/screen.model';

export interface SyncStatus {
  isInitialSync: boolean;
  isComplete: boolean;
  currentStep: string;
  progress: number;
  totalPlaylists: number;
  downloadedPlaylists: number;
  totalContent: number;
  downloadedContent: number;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class StartupSyncService {
  private syncStatusSubject = new BehaviorSubject<SyncStatus>({
    isInitialSync: false,
    isComplete: false,
    currentStep: 'Initializing...',
    progress: 0,
    totalPlaylists: 0,
    downloadedPlaylists: 0,
    totalContent: 0,
    downloadedContent: 0
  });

  public syncStatus$ = this.syncStatusSubject.asObservable();
  private readonly CACHE_VERSION_KEY = 'cache_version';
  private readonly LAST_SYNC_KEY = 'last_full_sync';
  private readonly SYNC_INTERVAL_HOURS = 24;

  constructor(
    private supabaseApi: SupabaseApiService,
    private contentSync: ContentSyncService,
    private deviceRegistration: DeviceRegistrationService,
    private scheduleService: ScheduleService,
    private logService: LogService
  ) {}

  async initializeApp(): Promise<boolean> {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
        this.logService.warn('Browser APIs not available in this environment');
        return false;
      }
      
      this.updateSyncStatus({ currentStep: 'Checking device registration...', progress: 5 });
      
      // Wait for device registration
      const deviceId = localStorage.getItem('deviceId');
      if (!deviceId) {
        throw new Error('Device registration failed');
      }

      this.updateSyncStatus({ currentStep: 'Checking cache status...', progress: 10 });

      // Check if we need a full sync
      const needsSync = await this.needsFullSync();
      
      if (needsSync) {
        this.updateSyncStatus({ 
          isInitialSync: true,
          currentStep: 'Starting full synchronization...', 
          progress: 15 
        });
        
        return await this.performFullSync(deviceId);
      } else {
        this.updateSyncStatus({ 
          currentStep: 'Using cached content', 
          progress: 100,
          isComplete: true 
        });
        
        // Quick validation sync in background
        this.performBackgroundValidation(deviceId);
        return true;
      }
    } catch (error) {
      this.logService.error('Startup sync failed', error);
      this.updateSyncStatus({ 
        error: `Initialization failed: ${error}`,
        currentStep: 'Sync failed - using cached content',
        isComplete: true
      });
      return false;
    }
  }

  private async needsFullSync(): Promise<boolean> {
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return true;
    }
    
    const lastSync = localStorage.getItem(this.LAST_SYNC_KEY);
    if (!lastSync) return true;

    const lastSyncTime = new Date(lastSync);
    const now = new Date();
    const hoursSinceSync = (now.getTime() - lastSyncTime.getTime()) / (1000 * 60 * 60);

    // Check if cache exists
    const hasCachedPlaylists = await this.contentSync.hasCachedContent();
    
    return hoursSinceSync >= this.SYNC_INTERVAL_HOURS || !hasCachedPlaylists;
  }

  private async performFullSync(screenId: string): Promise<boolean> {
    try {
      // Get screen info
      this.updateSyncStatus({ currentStep: 'Loading screen configuration...', progress: 20 });
      const screen = await this.supabaseApi.getScreenById(screenId).toPromise();
      if (!screen) throw new Error('Screen not found');

      // Get all relevant playlists
      this.updateSyncStatus({ currentStep: 'Discovering playlists...', progress: 25 });
      const playlistIds = await this.getRelevantPlaylistIds(screen);
      
      this.updateSyncStatus({ 
        totalPlaylists: playlistIds.length,
        currentStep: 'Loading playlist data...', 
        progress: 30 
      });

      // Download all playlist metadata
      const playlists = await this.downloadAllPlaylists(playlistIds);
      
      // Calculate total content items
      const totalContentItems = playlists.reduce((total, playlist) => total + playlist.items.length, 0);
      this.updateSyncStatus({ 
        totalContent: totalContentItems,
        currentStep: 'Downloading content...', 
        progress: 40 
      });

      // Download all content
      await this.downloadAllContent(playlists);

      // Cache schedules
      this.updateSyncStatus({ currentStep: 'Caching schedules...', progress: 90 });
      await this.cacheScheduleData(screen);

      // Mark sync complete
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem(this.LAST_SYNC_KEY, new Date().toISOString());
      }
      this.updateSyncStatus({ 
        currentStep: 'Synchronization complete', 
        progress: 100,
        isComplete: true 
      });

      this.logService.info(`Full sync completed: ${playlists.length} playlists, ${totalContentItems} content items`);
      return true;

    } catch (error) {
      this.logService.error('Full sync failed', error);
      this.updateSyncStatus({ 
        error: `Sync failed: ${error}`,
        currentStep: 'Sync failed - using cached content',
        isComplete: true
      });
      return false;
    }
  }

  private async getRelevantPlaylistIds(screen: Screen): Promise<string[]> {
    const playlistIds = new Set<string>();

    // Add current playlist
    if (screen.current_playlist) {
      playlistIds.add(screen.current_playlist);
    }

    // Get area information separately to find default playlist
    try {
      const area = await this.supabaseApi.getScreenArea(screen.id).toPromise();
      if (area?.current_playlist) {
        playlistIds.add(area.current_playlist);
      }
    } catch (error) {
      this.logService.warn('Could not fetch area information', error);
    }

    // Add scheduled playlists (next 7 days)
    const scheduledPlaylists = await this.supabaseApi.getScheduledPlaylistsForScreen(screen.id, 7).toPromise();
    scheduledPlaylists?.forEach(schedule => {
      if (schedule.playlist_id) {
        playlistIds.add(schedule.playlist_id);
      }
    });

    return Array.from(playlistIds);
  }

  private async downloadAllPlaylists(playlistIds: string[]): Promise<Playlist[]> {
    const playlists: Playlist[] = [];
    
    for (let i = 0; i < playlistIds.length; i++) {
      const playlistId = playlistIds[i];
      try {
        const playlist = await this.supabaseApi.getPlaylistById(playlistId).toPromise();
        if (playlist) {
          playlists.push(playlist);
          this.updateSyncStatus({ 
            downloadedPlaylists: i + 1,
            progress: 30 + (i + 1) / playlistIds.length * 10
          });
        }
      } catch (error) {
        this.logService.error(`Failed to download playlist ${playlistId}`, error);
      }
    }

    return playlists;
  }

  private async downloadAllContent(playlists: Playlist[]): Promise<void> {
    let downloadedCount = 0;
    const allItems = playlists.flatMap(playlist => playlist.items);

    for (const item of allItems) {
      try {
        if (item.content?.url) {
          await this.contentSync.cacheContent(item.content.url).toPromise();
        }
        downloadedCount++;
        this.updateSyncStatus({ 
          downloadedContent: downloadedCount,
          progress: 40 + (downloadedCount / allItems.length) * 45
        });
      } catch (error) {
        this.logService.error(`Failed to cache content: ${item.content?.url}`, error);
        downloadedCount++;
      }
    }
  }

  private async cacheScheduleData(screen: Screen): Promise<void> {
    try {
      // Cache current schedule for the next 7 days
      const schedules = await this.supabaseApi.getScheduledPlaylistsForScreen(screen.id, 7).toPromise();
      if (schedules && typeof localStorage !== 'undefined') {
        localStorage.setItem(`cached_schedules_${screen.id}`, JSON.stringify(schedules));
      }
    } catch (error) {
      this.logService.error('Failed to cache schedule data', error);
    }
  }

  private async performBackgroundValidation(screenId: string): Promise<void> {
    try {
      this.logService.info('Starting background cache validation');
      
      // Check for any new or updated playlists
      const screen = await this.supabaseApi.getScreenById(screenId).toPromise();
      if (!screen) return;

      const playlistIds = await this.getRelevantPlaylistIds(screen);
      const cachedPlaylists = await this.contentSync.getAllCachedPlaylists().toPromise() || [];
      
      // Check each required playlist for updates
      for (const playlistId of playlistIds) {
        const cachedPlaylist = cachedPlaylists.find(p => p.id === playlistId);
        
        if (!cachedPlaylist) {
          this.logService.info(`Missing playlist in cache: ${playlistId}`);
          // Download missing playlist
          const playlist = await this.supabaseApi.getPlaylistById(playlistId).toPromise();
          if (playlist) {
            this.contentSync.cachePlaylist(playlist);
            await this.downloadAllContent([playlist]);
          }
        } else {
          // Check if cached version is outdated
          const freshPlaylist = await this.supabaseApi.getPlaylistById(playlistId).toPromise();
          if (freshPlaylist && this.isPlaylistUpdated(cachedPlaylist, freshPlaylist)) {
            this.logService.info(`Updating outdated playlist: ${playlistId}`);
            this.contentSync.cachePlaylist(freshPlaylist);
            await this.downloadAllContent([freshPlaylist]);
          }
        }
      }

      // Clean up unused cached playlists
      await this.cleanupUnusedCache(playlistIds, cachedPlaylists);
      
      this.logService.info('Background cache validation completed');
    } catch (error) {
      this.logService.error('Background validation failed', error);
    }
  }

  private isPlaylistUpdated(cached: Playlist, fresh: Playlist): boolean {
    // Compare last modified timestamps
    if (!cached.lastModified || !fresh.lastModified) {
      return true; // Assume updated if timestamps missing
    }
    
    const cachedTime = new Date(cached.lastModified).getTime();
    const freshTime = new Date(fresh.lastModified).getTime();
    
    return freshTime > cachedTime;
  }

  private async cleanupUnusedCache(requiredPlaylistIds: string[], cachedPlaylists: Playlist[]): Promise<void> {
    const unusedPlaylists = cachedPlaylists.filter(cached => 
      !requiredPlaylistIds.includes(cached.id)
    );

    // Keep some buffer playlists but remove very old ones
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days
    const now = Date.now();

    for (const unused of unusedPlaylists) {
      const cacheAge = now - new Date(unused.lastModified || 0).getTime();
      if (cacheAge > maxAge) {
        this.logService.info(`Removing old unused playlist from cache: ${unused.id}`);
        await this.contentSync.deleteCachedPlaylist(unused.id).toPromise();
        
        // Also remove associated content
        for (const item of unused.items) {
          if (item.content?.url) {
            await this.contentSync.deleteFromCache(item.content.url).toPromise();
          }
        }
      }
    }
  }

  /**
   * Force a manual cache refresh
   */
  async refreshCache(): Promise<boolean> {
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return false;
    }
    
    const deviceId = localStorage.getItem('deviceId');
    if (!deviceId) {
      return false;
    }

    this.updateSyncStatus({
      isInitialSync: true,
      currentStep: 'Manual cache refresh...',
      progress: 0
    });

    return await this.performFullSync(deviceId);
  }

  /**
   * Get cache validation status
   */
  async getCacheValidationStatus(): Promise<{
    isValid: boolean;
    lastValidation: Date | null;
    cacheSize: number;
    playlistCount: number;
  }> {
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return {
        isValid: false,
        lastValidation: null,
        cacheSize: 0,
        playlistCount: 0
      };
    }
    
    const lastSync = localStorage.getItem(this.LAST_SYNC_KEY);
    const stats = await this.contentSync.getCacheStats().toPromise();
    
    return {
      isValid: !!lastSync && (stats?.playlistCount || 0) > 0,
      lastValidation: lastSync ? new Date(lastSync) : null,
      cacheSize: stats?.totalSize || 0,
      playlistCount: stats?.playlistCount || 0
    };
  }

  private updateSyncStatus(update: Partial<SyncStatus>): void {
    const current = this.syncStatusSubject.value;
    this.syncStatusSubject.next({ ...current, ...update });
  }

  getSyncStatus(): SyncStatus {
    return this.syncStatusSubject.value;
  }

  isSyncComplete(): boolean {
    return this.syncStatusSubject.value.isComplete;
  }
}