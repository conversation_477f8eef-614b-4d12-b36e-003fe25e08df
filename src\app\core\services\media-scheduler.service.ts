// media-scheduler.service.ts
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, interval, combineLatest } from 'rxjs';
import { map, distinctUntilChanged, tap, switchMap, catchError } from 'rxjs/operators';
import { LogService } from './log.service';
import { SupabaseApiService } from './supabase-api.service';
import { ContentSyncService } from './content-sync.service';
import { Playlist, PlaylistItem } from '../models/playlist.model';

// Enhanced schedule interfaces
interface ScheduleTimeSlot {
  id: string;
  playlistId: string;
  playlistName: string;
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
  daysOfWeek: string[]; // ['Monday', 'Tuesday', etc.]
  priority: number; // Lower number = higher priority
  timezone?: string; // Optional timezone override
  isActive: boolean;
  settings?: {
    transitionDuration?: number; // Custom transition duration in ms
    preloadTime?: number; // How many minutes before to start preloading
  };
}

interface ScheduleState {
  currentSchedule: ScheduleTimeSlot | null;
  nextSchedule: ScheduleTimeSlot | null;
  currentPlaylistId: string | null;
  nextTransitionTime: Date | null;
  isTransitioning: boolean;
  preloadingNextPlaylist: boolean;
}

interface PlaylistTransition {
  fromPlaylistId: string | null;
  toPlaylistId: string;
  transitionTime: Date;
  transitionDuration: number;
  preloadCompleted: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class MediaSchedulerService {
  private readonly CHECK_INTERVAL_MS = 30000; // Check every 30 seconds
  private readonly PRELOAD_MINUTES = 5; // Start preloading 5 minutes before transition
  private readonly DEFAULT_TRANSITION_DURATION = 1000; // 1 second default transition

  private scheduleState$ = new BehaviorSubject<ScheduleState>({
    currentSchedule: null,
    nextSchedule: null,
    currentPlaylistId: null,
    nextTransitionTime: null,
    isTransitioning: false,
    preloadingNextPlaylist: false
  });

  private schedules: ScheduleTimeSlot[] = [];
  private deviceId: string | null = null;
  private currentTimezone: string = Intl.DateTimeFormat().resolvedOptions().timeZone;

  constructor(
    private logService: LogService,
    private supabaseApi: SupabaseApiService,
    private contentSync: ContentSyncService
  ) {
    this.initializeScheduler();
  }

  /**
   * Initialize the scheduler
   */
  private initializeScheduler(): void {
    if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
      this.deviceId = localStorage.getItem('deviceId');
    }

    if (!this.deviceId) {
      this.logService.warn('No device ID found, scheduler cannot start');
      return;
    }

    this.logService.info('Initializing Media Scheduler');
    this.loadSchedules();
    this.startScheduleMonitoring();
  }

  /**
   * Load schedules for the current device
   */
  private loadSchedules(): void {
    if (!this.deviceId) return;

    this.supabaseApi.getScreenById(this.deviceId).pipe(
      switchMap(screen => {
        if (!screen) {
          throw new Error('Screen not found');
        }
        
        // Get all playlists assigned to this screen's area
        return this.supabaseApi.getScreenArea(this.deviceId!);
      }),
      switchMap(area => {
        if (!area) {
          throw new Error('Area not found');
        }
        
        // Get all scheduled playlists for this area
        return this.supabaseApi.getScheduledPlaylists(area.id);
      }),
      catchError(error => {
        this.logService.error(`Error loading schedules: ${error.message}`);
        return [];
      })
    ).subscribe(schedules => {
      this.schedules = this.parseSchedules(schedules);
      this.logService.info(`Loaded ${this.schedules.length} schedule entries`);
      this.updateCurrentSchedule();
    });
  }

  /**
   * Parse raw schedule data into ScheduleTimeSlot objects
   */
  private parseSchedules(rawSchedules: any[]): ScheduleTimeSlot[] {
    return rawSchedules.map(schedule => ({
      id: schedule.id,
      playlistId: schedule.playlist_id,
      playlistName: schedule.playlist_name || 'Unknown Playlist',
      startTime: schedule.start_time,
      endTime: schedule.end_time,
      daysOfWeek: schedule.days_of_week || [],
      priority: schedule.priority || 0,
      timezone: schedule.timezone || this.currentTimezone,
      isActive: schedule.is_active !== false,
      settings: {
        transitionDuration: schedule.transition_duration || this.DEFAULT_TRANSITION_DURATION,
        preloadTime: schedule.preload_time || this.PRELOAD_MINUTES
      }
    })).filter(schedule => schedule.isActive);
  }

  /**
   * Start monitoring schedule changes
   */
  private startScheduleMonitoring(): void {
    // Check schedule every 30 seconds
    interval(this.CHECK_INTERVAL_MS).pipe(
      tap(() => this.updateCurrentSchedule())
    ).subscribe();

    // Also check on minute boundaries for more precise transitions
    interval(1000).pipe(
      map(() => new Date()),
      distinctUntilChanged((prev, curr) => prev.getMinutes() === curr.getMinutes()),
      tap(() => this.updateCurrentSchedule())
    ).subscribe();
  }

  /**
   * Update current schedule based on current time
   */
  private updateCurrentSchedule(): void {
    const now = new Date();
    const currentTime = this.formatTime(now);
    const currentDay = this.getDayName(now);

    // Find matching schedules for current time
    const matchingSchedules = this.schedules.filter(schedule => 
      this.isScheduleActive(schedule, currentTime, currentDay)
    );

    // Sort by priority (lower number = higher priority)
    matchingSchedules.sort((a, b) => a.priority - b.priority);

    const currentSchedule = matchingSchedules[0] || null;
    const currentState = this.scheduleState$.value;

    // Check if schedule changed
    if (currentSchedule?.playlistId !== currentState.currentPlaylistId) {
      this.logService.info(`Schedule change detected: ${currentState.currentPlaylistId} -> ${currentSchedule?.playlistId}`);
      
      if (currentSchedule) {
        this.initiatePlaylistTransition(currentSchedule);
      }
    }

    // Find next schedule
    const nextSchedule = this.findNextSchedule(now);
    
    this.scheduleState$.next({
      ...currentState,
      currentSchedule,
      nextSchedule,
      currentPlaylistId: currentSchedule?.playlistId || null,
      nextTransitionTime: nextSchedule ? this.calculateNextTransitionTime(nextSchedule, now) : null
    });

    // Handle preloading
    if (nextSchedule && !currentState.preloadingNextPlaylist) {
      this.handlePreloading(nextSchedule, now);
    }
  }

  /**
   * Check if a schedule is active for the given time and day
   */
  private isScheduleActive(schedule: ScheduleTimeSlot, currentTime: string, currentDay: string): boolean {
    // Check day of week
    if (!schedule.daysOfWeek.includes(currentDay)) {
      return false;
    }

    // Check time range
    const start = schedule.startTime;
    const end = schedule.endTime;

    // Handle overnight schedules (e.g., 23:00 - 01:00)
    if (start > end) {
      return currentTime >= start || currentTime <= end;
    } else {
      return currentTime >= start && currentTime <= end;
    }
  }

  /**
   * Find the next scheduled playlist
   */
  private findNextSchedule(now: Date): ScheduleTimeSlot | null {
    const currentTime = this.formatTime(now);
    const currentDay = this.getDayName(now);
    
    // Look for schedules later today
    const todaySchedules = this.schedules
      .filter(s => s.daysOfWeek.includes(currentDay) && s.startTime > currentTime)
      .sort((a, b) => a.startTime.localeCompare(b.startTime));

    if (todaySchedules.length > 0) {
      return todaySchedules[0];
    }

    // Look for schedules tomorrow and beyond
    for (let daysAhead = 1; daysAhead <= 7; daysAhead++) {
      const futureDate = new Date(now);
      futureDate.setDate(futureDate.getDate() + daysAhead);
      const futureDay = this.getDayName(futureDate);

      const futureDaySchedules = this.schedules
        .filter(s => s.daysOfWeek.includes(futureDay))
        .sort((a, b) => a.startTime.localeCompare(b.startTime));

      if (futureDaySchedules.length > 0) {
        return futureDaySchedules[0];
      }
    }

    return null;
  }

  /**
   * Calculate when the next transition will occur
   */
  private calculateNextTransitionTime(schedule: ScheduleTimeSlot, now: Date): Date {
    const [hours, minutes] = schedule.startTime.split(':').map(Number);
    const transitionTime = new Date(now);
    
    // Find the next occurrence of this schedule
    for (let daysAhead = 0; daysAhead <= 7; daysAhead++) {
      const testDate = new Date(now);
      testDate.setDate(testDate.getDate() + daysAhead);
      testDate.setHours(hours, minutes, 0, 0);
      
      const testDay = this.getDayName(testDate);
      
      if (schedule.daysOfWeek.includes(testDay) && testDate > now) {
        return testDate;
      }
    }

    return transitionTime;
  }

  /**
   * Handle preloading of next playlist
   */
  private handlePreloading(nextSchedule: ScheduleTimeSlot, now: Date): void {
    const nextTransitionTime = this.calculateNextTransitionTime(nextSchedule, now);
    const preloadTime = nextSchedule.settings?.preloadTime || this.PRELOAD_MINUTES;
    const preloadStartTime = new Date(nextTransitionTime.getTime() - (preloadTime * 60 * 1000));

    if (now >= preloadStartTime) {
      this.logService.info(`Starting preload for playlist: ${nextSchedule.playlistId}`);
      this.preloadPlaylist(nextSchedule.playlistId);
      
      const currentState = this.scheduleState$.value;
      this.scheduleState$.next({
        ...currentState,
        preloadingNextPlaylist: true
      });
    }
  }

  /**
   * Preload a playlist and its content
   */
  private preloadPlaylist(playlistId: string): void {
    this.supabaseApi.getPlaylistById(playlistId).subscribe({
      next: (playlist) => {
        if (playlist) {
          // Cache the playlist
          this.contentSync.cachePlaylist(playlist);
          
          // Download all media content
          this.contentSync.downloadScheduledMedia([playlist]).subscribe({
            next: (status) => {
              if (status.isComplete) {
                this.logService.info(`Preload completed for playlist: ${playlistId}`);
              }
            },
            error: (error) => {
              this.logService.error(`Preload failed for playlist ${playlistId}: ${error.message}`);
            }
          });
        }
      },
      error: (error) => {
        this.logService.error(`Failed to fetch playlist for preload: ${error.message}`);
      }
    });
  }

  /**
   * Initiate playlist transition
   */
  private initiatePlaylistTransition(newSchedule: ScheduleTimeSlot): void {
    const currentState = this.scheduleState$.value;
    const transition: PlaylistTransition = {
      fromPlaylistId: currentState.currentPlaylistId,
      toPlaylistId: newSchedule.playlistId,
      transitionTime: new Date(),
      transitionDuration: newSchedule.settings?.transitionDuration || this.DEFAULT_TRANSITION_DURATION,
      preloadCompleted: currentState.preloadingNextPlaylist
    };

    this.logService.info(`Initiating playlist transition:`, transition);

    // Update state to indicate transition is happening
    this.scheduleState$.next({
      ...currentState,
      isTransitioning: true,
      preloadingNextPlaylist: false
    });

    // The actual playlist change will be handled by the PlaybackService
    // This service just manages the scheduling logic
  }

  /**
   * Utility methods
   */
  private formatTime(date: Date): string {
    return date.toTimeString().substring(0, 5); // HH:mm format
  }

  private getDayName(date: Date): string {
    return date.toLocaleDateString('en-US', { weekday: 'long' });
  }

  /**
   * Public API methods
   */
  getScheduleState(): Observable<ScheduleState> {
    return this.scheduleState$.asObservable();
  }

  getCurrentSchedule(): ScheduleTimeSlot | null {
    return this.scheduleState$.value.currentSchedule;
  }

  getNextSchedule(): ScheduleTimeSlot | null {
    return this.scheduleState$.value.nextSchedule;
  }

  refreshSchedules(): void {
    this.loadSchedules();
  }

  /**
   * Force a schedule check (useful for testing)
   */
  forceScheduleCheck(): void {
    this.updateCurrentSchedule();
  }
}
