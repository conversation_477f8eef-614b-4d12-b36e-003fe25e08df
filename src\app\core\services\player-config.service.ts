// player-config.service.ts
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { LogService } from './log.service';

// Configuration interfaces
export interface CarouselConfig {
  transitionDuration: number; // in milliseconds
  transitionType: 'slide' | 'fade' | 'crossfade' | 'none';
  autoAdvance: boolean;
  defaultItemDuration: number; // in seconds
  preloadNextItems: number; // how many items ahead to preload
  pauseOnError: boolean;
  errorDisplayDuration: number; // how long to show error before continuing
}

export interface PlaylistTransitionConfig {
  enabled: boolean;
  duration: number; // transition duration between playlists
  type: 'crossfade' | 'slide' | 'immediate';
  preloadTime: number; // minutes before transition to start preloading
}

export interface MediaDownloadConfig {
  maxConcurrentDownloads: number;
  maxRetryAttempts: number;
  retryDelayMs: number;
  enableProgressTracking: boolean;
  preloadScheduledContent: boolean;
}

export interface SchedulingConfig {
  checkIntervalMs: number;
  preloadMinutes: number;
  enableTimezoneHandling: boolean;
  defaultTimezone: string;
  enableScheduleValidation: boolean;
}

export interface PlayerConfiguration {
  carousel: CarouselConfig;
  playlistTransition: PlaylistTransitionConfig;
  mediaDownload: MediaDownloadConfig;
  scheduling: SchedulingConfig;
  debug: {
    enableVerboseLogging: boolean;
    logPerformanceMetrics: boolean;
    showDiagnosticOverlay: boolean;
  };
}

@Injectable({
  providedIn: 'root'
})
export class PlayerConfigService {
  private readonly CONFIG_STORAGE_KEY = 'player-configuration';
  
  // Default configuration
  private defaultConfig: PlayerConfiguration = {
    carousel: {
      transitionDuration: 500,
      transitionType: 'slide',
      autoAdvance: true,
      defaultItemDuration: 10,
      preloadNextItems: 2,
      pauseOnError: false,
      errorDisplayDuration: 3
    },
    playlistTransition: {
      enabled: true,
      duration: 1000,
      type: 'crossfade',
      preloadTime: 5
    },
    mediaDownload: {
      maxConcurrentDownloads: 3,
      maxRetryAttempts: 3,
      retryDelayMs: 2000,
      enableProgressTracking: true,
      preloadScheduledContent: true
    },
    scheduling: {
      checkIntervalMs: 30000,
      preloadMinutes: 5,
      enableTimezoneHandling: true,
      defaultTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      enableScheduleValidation: true
    },
    debug: {
      enableVerboseLogging: false,
      logPerformanceMetrics: false,
      showDiagnosticOverlay: true
    }
  };

  private configSubject = new BehaviorSubject<PlayerConfiguration>(this.defaultConfig);
  private currentConfig: PlayerConfiguration;

  constructor(private logService: LogService) {
    this.currentConfig = this.loadConfiguration();
    this.configSubject.next(this.currentConfig);
  }

  /**
   * Get current configuration as observable
   */
  getConfig(): Observable<PlayerConfiguration> {
    return this.configSubject.asObservable();
  }

  /**
   * Get current configuration synchronously
   */
  getCurrentConfig(): PlayerConfiguration {
    return this.currentConfig;
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<PlayerConfiguration>): void {
    this.currentConfig = this.mergeConfig(this.currentConfig, updates);
    this.saveConfiguration(this.currentConfig);
    this.configSubject.next(this.currentConfig);
    
    this.logService.info('Player configuration updated', updates);
  }

  /**
   * Update carousel configuration
   */
  updateCarouselConfig(updates: Partial<CarouselConfig>): void {
    this.updateConfig({
      carousel: { ...this.currentConfig.carousel, ...updates }
    });
  }

  /**
   * Update playlist transition configuration
   */
  updatePlaylistTransitionConfig(updates: Partial<PlaylistTransitionConfig>): void {
    this.updateConfig({
      playlistTransition: { ...this.currentConfig.playlistTransition, ...updates }
    });
  }

  /**
   * Update media download configuration
   */
  updateMediaDownloadConfig(updates: Partial<MediaDownloadConfig>): void {
    this.updateConfig({
      mediaDownload: { ...this.currentConfig.mediaDownload, ...updates }
    });
  }

  /**
   * Update scheduling configuration
   */
  updateSchedulingConfig(updates: Partial<SchedulingConfig>): void {
    this.updateConfig({
      scheduling: { ...this.currentConfig.scheduling, ...updates }
    });
  }

  /**
   * Apply playlist-specific settings
   */
  applyPlaylistSettings(playlistSettings: any): void {
    if (!playlistSettings) return;

    const updates: Partial<PlayerConfiguration> = {};

    // Apply transition settings
    if (playlistSettings.transition) {
      updates.carousel = {
        ...this.currentConfig.carousel,
        transitionType: playlistSettings.transition.type || this.currentConfig.carousel.transitionType,
        transitionDuration: playlistSettings.transition.duration || this.currentConfig.carousel.transitionDuration
      };
    }

    // Apply default duration
    if (playlistSettings.defaultDuration) {
      updates.carousel = {
        ...updates.carousel || this.currentConfig.carousel,
        defaultItemDuration: playlistSettings.defaultDuration
      };
    }

    // Apply auto-play setting
    if (typeof playlistSettings.autoPlay === 'boolean') {
      updates.carousel = {
        ...updates.carousel || this.currentConfig.carousel,
        autoAdvance: playlistSettings.autoPlay
      };
    }

    if (Object.keys(updates).length > 0) {
      this.updateConfig(updates);
      this.logService.info('Applied playlist-specific settings', updates);
    }
  }

  /**
   * Reset to default configuration
   */
  resetToDefaults(): void {
    this.currentConfig = { ...this.defaultConfig };
    this.saveConfiguration(this.currentConfig);
    this.configSubject.next(this.currentConfig);
    
    this.logService.info('Player configuration reset to defaults');
  }

  /**
   * Load configuration from storage
   */
  private loadConfiguration(): PlayerConfiguration {
    try {
      if (typeof localStorage !== 'undefined') {
        const stored = localStorage.getItem(this.CONFIG_STORAGE_KEY);
        if (stored) {
          const parsedConfig = JSON.parse(stored);
          return this.mergeConfig(this.defaultConfig, parsedConfig);
        }
      }
    } catch (error) {
      this.logService.warn('Failed to load stored configuration, using defaults', error);
    }
    
    return { ...this.defaultConfig };
  }

  /**
   * Save configuration to storage
   */
  private saveConfiguration(config: PlayerConfiguration): void {
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem(this.CONFIG_STORAGE_KEY, JSON.stringify(config));
      }
    } catch (error) {
      this.logService.error('Failed to save configuration', error);
    }
  }

  /**
   * Deep merge configuration objects
   */
  private mergeConfig(base: PlayerConfiguration, updates: any): PlayerConfiguration {
    const result = { ...base };
    
    for (const key in updates) {
      if (updates.hasOwnProperty(key)) {
        if (typeof updates[key] === 'object' && updates[key] !== null && !Array.isArray(updates[key])) {
          result[key as keyof PlayerConfiguration] = {
            ...result[key as keyof PlayerConfiguration] as any,
            ...updates[key]
          };
        } else {
          result[key as keyof PlayerConfiguration] = updates[key];
        }
      }
    }
    
    return result;
  }

  /**
   * Validate configuration
   */
  validateConfig(config: PlayerConfiguration): boolean {
    try {
      // Basic validation
      if (config.carousel.transitionDuration < 0) return false;
      if (config.carousel.defaultItemDuration <= 0) return false;
      if (config.carousel.preloadNextItems < 0) return false;
      if (config.playlistTransition.duration < 0) return false;
      if (config.mediaDownload.maxConcurrentDownloads <= 0) return false;
      if (config.scheduling.checkIntervalMs <= 0) return false;
      
      return true;
    } catch (error) {
      this.logService.error('Configuration validation failed', error);
      return false;
    }
  }

  /**
   * Get configuration for specific component
   */
  getCarouselConfig(): CarouselConfig {
    return this.currentConfig.carousel;
  }

  getPlaylistTransitionConfig(): PlaylistTransitionConfig {
    return this.currentConfig.playlistTransition;
  }

  getMediaDownloadConfig(): MediaDownloadConfig {
    return this.currentConfig.mediaDownload;
  }

  getSchedulingConfig(): SchedulingConfig {
    return this.currentConfig.scheduling;
  }
}
