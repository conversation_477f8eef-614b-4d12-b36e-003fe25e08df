<!-- player.component.html - Carousel-based Digital Signage Player -->
<div class="player-container">
  <!-- Error overlay - shown when there's a playback error or device validation error -->
  <div *ngIf="playbackError || deviceValidationError" class="error-overlay">
    <div class="error-container">
      <div class="error-icon">
        <span class="material-icons">{{ deviceValidationError ? 'error_outline' : 'display_settings' }}</span>
      </div>
      <h2 class="error-title">{{ deviceValidationError ? 'Device Not Found' : 'Display Not Ready' }}</h2>
      <p class="error-message">
        {{ deviceValidationError ? 'This device is no longer registered in the system.' : 'No content has been assigned to this display yet.' }}
      </p>
      <p class="error-submessage">
        {{ deviceValidationError ? 'Please contact your administrator to re-register this device.' : 'Please assign content to this display in the management dashboard.' }}
      </p>
      <button class="reload-button" (click)="reloadPlaylist()">
        <span class="material-icons">refresh</span>
        Try Again
      </button>
    </div>
  </div>

  <!-- Offline banner -->
  <div *ngIf="!isOnline" class="offline-banner">
    <span class="material-icons">wifi_off</span>
    <span>Running in offline mode</span>
  </div>

  <!-- Main carousel container -->
  <div class="carousel-wrapper" *ngIf="!playbackError && !deviceValidationError">
    <div
      class="carousel-container"
      #carouselContainer
      [style.width.px]="carouselItems.length * 100 + 'vw'"
      [style.transform]="'translateX(' + (-currentIndex * 100) + 'vw)'"
    >
      <!-- Carousel items -->
      <div
        *ngFor="let carouselItem of carouselItems; let i = index"
        class="carousel-item"
        [class.active]="i === currentIndex"
      >
        <!-- Loading indicator -->
        <div *ngIf="!carouselItem.isLoaded" class="loading-indicator">
          <div class="spinner"></div>
          <p>Loading content...</p>
        </div>

        <!-- Content based on type -->
        <ng-container *ngIf="carouselItem.isLoaded" [ngSwitch]="carouselItem.playlistItem.type">
          <!-- Image content -->
          <div *ngSwitchCase="'image'" class="content-item image-content">
            <img
              *ngIf="carouselItem.localContentUrl && !carouselItem.hasError"
              [src]="carouselItem.localContentUrl"
              [alt]="carouselItem.playlistItem.name"
              class="fullscreen-image"
              (error)="onImageError(carouselItem)"
              (load)="onImageLoad(carouselItem)"
            />
            <div *ngIf="carouselItem.hasError" class="error-placeholder">
              <span class="material-icons">broken_image</span>
              <p>Image could not be loaded</p>
            </div>
          </div>

          <!-- Video content -->
          <div *ngSwitchCase="'video'" class="content-item video-content">
            <video
              *ngIf="carouselItem.localContentUrl && !carouselItem.hasError"
              [src]="carouselItem.localContentUrl"
              [muted]="carouselItem.playlistItem.settings.muted ?? true"
              [loop]="carouselItem.playlistItem.settings.loop ?? false"
              [autoplay]="i === currentIndex"
              playsinline
              preload="metadata"
              class="fullscreen-video"
              (ended)="slideToNext()"
              (error)="onVideoError(carouselItem)"
            ></video>
            <div *ngIf="carouselItem.hasError" class="error-placeholder">
              <span class="material-icons">videocam_off</span>
              <p>Video could not be loaded</p>
            </div>
          </div>

          <!-- Web content -->
          <div *ngSwitchCase="'webpage'" class="content-item web-content">
            <iframe
              *ngIf="carouselItem.localContentUrl && !carouselItem.hasError"
              [src]="carouselItem.localContentUrl"
              class="fullscreen-iframe"
              frameborder="0"
              allowfullscreen
            ></iframe>
            <div *ngIf="carouselItem.hasError" class="error-placeholder">
              <span class="material-icons">web_off</span>
              <p>Web content could not be loaded</p>
            </div>
          </div>

          <!-- Ticker/text content -->
          <div *ngSwitchCase="'ticker'" class="content-item ticker-content">
            <div class="ticker-wrapper">
              <div class="ticker-text">
                {{ carouselItem.playlistItem.content.url }}
              </div>
            </div>
          </div>

          <!-- Fallback for unknown content type -->
          <div *ngSwitchDefault class="content-item fallback-content">
            <span class="material-icons">image_not_supported</span>
            <p>Unsupported content type: {{ carouselItem.playlistItem.type }}</p>
          </div>
        </ng-container>
      </div>
    </div>

    <!-- Carousel indicators (optional) -->
    <div class="carousel-indicators" *ngIf="carouselItems.length > 1">
      <div
        *ngFor="let item of carouselItems; let i = index"
        class="indicator"
        [class.active]="i === currentIndex"
      ></div>
    </div>
  </div>

  <!-- Diagnostic overlay -->
  <div *ngIf="playerState$ | async as state" class="diagnostics-overlay">
    <div class="info-pill">
      <div class="status-indicator" [class.online]="isOnline" [class.offline]="!isOnline"></div>
      <span class="playlist-name">{{ state.currentPlaylistName }}</span>
      <span class="item-counter">{{ currentIndex + 1 }}/{{ carouselItems.length }}</span>
      <button class="control-button refresh-button" (click)="reloadPlaylist()" title="Reload Playlist">
        <span class="material-icons">refresh</span>
      </button>
    </div>
  </div>
</div>